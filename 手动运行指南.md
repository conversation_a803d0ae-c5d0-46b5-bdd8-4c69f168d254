# 手动运行指南

## 问题分析
您遇到的错误是因为Spark无法直接运行`.scala`源代码文件，需要先编译成`.class`文件。

## 解决方案

### 方法1: 使用新的编译脚本（推荐）
```bash
# 给新脚本执行权限
chmod +x compile_and_run.sh

# 运行新脚本
./compile_and_run.sh
```

### 方法2: 手动逐步执行
```bash
# 1. 确保在正确目录
cd /home/<USER>/spark_work

# 2. 检查文件是否存在
ls -la *.scala

# 3. 编译Scala文件
echo "编译 GeneratePeopleData.scala..."
scalac -cp "$SPARK_HOME/jars/*" GeneratePeopleData.scala

echo "编译 CalculateAverageAge.scala..."
scalac -cp "$SPARK_HOME/jars/*" CalculateAverageAge.scala

echo "编译 CoffeeChainAnalysis.scala..."
scalac -cp "$SPARK_HOME/jars/*" CoffeeChainAnalysis.scala

# 4. 检查编译结果
ls -la *.class

# 5. 运行程序
echo "运行任务1: 生成人口数据"
spark-submit --class GeneratePeopleData --master local[*] --driver-memory 1g ./ 1000

echo "运行任务2: 计算平均年龄"
spark-submit --class CalculateAverageAge --master local[*] --driver-memory 1g ./

echo "运行任务3: 咖啡数据分析（需要CoffeeChain.csv文件）"
spark-submit --class CoffeeChainAnalysis --master local[*] --driver-memory 1g ./
```

### 方法3: 使用spark-shell交互式运行
```bash
# 启动spark-shell
spark-shell --master local[*] --driver-memory 1g

# 在spark-shell中粘贴并运行代码
# 复制GeneratePeopleData.scala的内容（去掉object声明行）
# 然后运行main方法
```

## 常见问题解决

### 1. 如果scalac命令不存在
```bash
# 检查Scala是否安装
scala -version

# 如果没有安装，安装Scala
sudo yum install scala
# 或者下载安装
wget https://downloads.lightbend.com/scala/2.12.17/scala-2.12.17.tgz
tar -xzf scala-2.12.17.tgz
sudo mv scala-2.12.17 /opt/scala
export PATH=/opt/scala/bin:$PATH
```

### 2. 如果编译时出现classpath错误
```bash
# 检查SPARK_HOME环境变量
echo $SPARK_HOME

# 如果为空，设置它
export SPARK_HOME=/opt/spark
export PATH=$SPARK_HOME/bin:$PATH

# 使用完整路径编译
scalac -cp "/opt/spark/jars/*" GeneratePeopleData.scala
```

### 3. 如果内存不足
```bash
# 减少内存使用
spark-submit --class GeneratePeopleData --master local[2] --driver-memory 512m ./
```

### 4. 缺少CoffeeChain.csv文件
```bash
# 从原始目录复制文件
cp /path/to/original/CoffeeChain.csv /home/<USER>/spark_work/

# 或者创建一个示例文件用于测试
echo "Product,Sales,Profit,State,Market,Product Type,COGS" > CoffeeChain.csv
echo "Coffee,100.0,20.0,CA,West,Regular,80.0" >> CoffeeChain.csv
echo "Tea,50.0,10.0,NY,East,Herbal,40.0" >> CoffeeChain.csv
```

## 预期输出

### 成功运行后应该看到：
1. **peopleage.txt** - 包含1000行人口年龄数据
2. **average_age_result.txt** - 平均年龄计算结果
3. **coffee_analysis_report.txt** - 咖啡数据分析报告（如果有CSV文件）
4. 多个分析结果目录（*_analysis/）

### 检查结果：
```bash
# 查看生成的文件
ls -la /home/<USER>/spark_work/

# 查看人口数据文件
head -10 peopleage.txt

# 查看平均年龄结果
cat average_age_result.txt

# 查看咖啡分析报告
head -20 coffee_analysis_report.txt
```

## 调试技巧

### 1. 查看详细错误信息
```bash
# 运行时显示更多日志
spark-submit --class GeneratePeopleData --master local[*] --driver-memory 1g --conf spark.sql.adaptive.enabled=false ./ 1000 2>&1 | tee run.log
```

### 2. 检查Java和Spark版本兼容性
```bash
java -version
spark-submit --version
scala -version
```

### 3. 简化测试
```bash
# 先测试最简单的程序
spark-submit --class GeneratePeopleData --master local[1] --driver-memory 512m ./ 100
```

按照这个指南，您应该能够成功编译和运行所有的Scala程序！
