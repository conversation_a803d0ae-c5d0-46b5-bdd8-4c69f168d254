#!/bin/bash

# Spark分析任务运行脚本
# 使用方法: ./run_spark_analysis.sh

echo "=== Spark数据分析任务执行脚本 ==="
echo "执行时间: $(date)"
echo ""

# 设置Spark环境变量（请根据您的实际安装路径调整）
export SPARK_HOME=${SPARK_HOME:-/opt/spark}
export PATH=$SPARK_HOME/bin:$PATH

# 检查Spark是否安装
if ! command -v spark-submit &> /dev/null; then
    echo "错误: 未找到spark-submit命令"
    echo "请确保Spark已正确安装并设置了SPARK_HOME环境变量"
    exit 1
fi

# 检查Java是否安装
if ! command -v java &> /dev/null; then
    echo "错误: 未找到Java"
    echo "请确保Java已正确安装"
    exit 1
fi

echo "Spark环境检查通过"
echo "SPARK_HOME: $SPARK_HOME"
echo "Java版本: $(java -version 2>&1 | head -n 1)"
echo ""

# 创建工作目录
WORK_DIR="/home/<USER>/spark_work"
mkdir -p $WORK_DIR
echo "工作目录: $WORK_DIR"
echo ""

# 复制必要的文件到工作目录
echo "复制文件到工作目录..."
cp *.scala $WORK_DIR/
cp CoffeeChain.csv $WORK_DIR/ 2>/dev/null || echo "注意: CoffeeChain.csv文件未找到，请确保该文件存在"
echo ""

cd $WORK_DIR

echo "=== 任务1: 生成人口年龄数据 ==="
echo "编译并运行 GeneratePeopleData.scala..."
spark-submit --class GeneratePeopleData --master local[*] GeneratePeopleData.scala 1000
if [ $? -eq 0 ]; then
    echo "✓ 人口数据生成完成"
    echo "生成的文件: $WORK_DIR/peopleage.txt"
else
    echo "✗ 人口数据生成失败"
fi
echo ""

echo "=== 任务2: 计算平均年龄 ==="
echo "编译并运行 CalculateAverageAge.scala..."
spark-submit --class CalculateAverageAge --master local[*] CalculateAverageAge.scala
if [ $? -eq 0 ]; then
    echo "✓ 平均年龄计算完成"
    echo "结果文件: $WORK_DIR/average_age_result.txt"
else
    echo "✗ 平均年龄计算失败"
fi
echo ""

echo "=== 任务3: 咖啡连锁店数据分析 ==="
echo "编译并运行 CoffeeChainAnalysis.scala..."
if [ -f "CoffeeChain.csv" ]; then
    spark-submit --class CoffeeChainAnalysis --master local[*] CoffeeChainAnalysis.scala
    if [ $? -eq 0 ]; then
        echo "✓ 咖啡连锁店数据分析完成"
        echo "分析报告: $WORK_DIR/coffee_analysis_report.txt"
    else
        echo "✗ 咖啡连锁店数据分析失败"
    fi
else
    echo "✗ 未找到CoffeeChain.csv文件，跳过咖啡连锁店分析"
fi
echo ""

echo "=== 执行完成 ==="
echo "生成的文件列表:"
ls -la $WORK_DIR/*.txt 2>/dev/null || echo "未找到txt结果文件"
echo ""
echo "CSV分析结果目录:"
ls -la $WORK_DIR/*_analysis/ 2>/dev/null || echo "未找到CSV分析结果"
echo ""

echo "所有任务执行完成！"
echo "请检查 $WORK_DIR 目录下的结果文件"
